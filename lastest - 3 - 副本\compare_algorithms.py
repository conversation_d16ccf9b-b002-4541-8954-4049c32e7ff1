#!/usr/bin/env python3
"""
算法性能对比可视化脚本
比较MADDPG、SAC和MaxFlowMinCut算法在延迟和能耗方面的性能
"""

import matplotlib.pyplot as plt
import numpy as np

def create_algorithm_comparison():
    """创建算法性能对比图表"""
    
    # 算法数据
    algorithms = ['MADDPG', 'SAC', 'MaxFlowMinCut']
    delays = [6.14, 1.41, 0.75]  # 延迟 (秒)
    energies = [0.925, 0.27, 0.77]  # 能耗 (焦耳)
    
    # 颜色设置
    colors = ['#d62728', '#1f77b4', '#ffcc00']  # 红色、蓝色、黄色
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建1x2子图布局
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    fig.suptitle('算法性能对比', fontsize=16, fontweight='bold', y=0.95)
    
    # 设置柱状图参数
    bar_width = 0.6
    x_pos = np.arange(len(algorithms))
    
    # 1. 延迟对比图
    bars1 = ax1.bar(x_pos, delays, bar_width, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    
    ax1.set_title('延迟对比', fontsize=14, fontweight='bold', pad=20)
    ax1.set_xlabel('算法', fontsize=12)
    ax1.set_ylabel('延迟 (s)', fontsize=12)
    ax1.set_xticks(x_pos)
    ax1.set_xticklabels(algorithms, fontsize=11)
    ax1.grid(True, alpha=0.3, axis='y')
    ax1.tick_params(labelsize=10)
    
    # 在柱状图上添加数值标签
    for i, (bar, value) in enumerate(zip(bars1, delays)):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                f'{value:.2f}s', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    # 设置y轴范围，留出空间显示标签
    ax1.set_ylim(0, max(delays) * 1.15)
    
    # 2. 能耗对比图
    bars2 = ax2.bar(x_pos, energies, bar_width, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    
    ax2.set_title('能耗对比', fontsize=14, fontweight='bold', pad=20)
    ax2.set_xlabel('算法', fontsize=12)
    ax2.set_ylabel('能耗 (J)', fontsize=12)
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels(algorithms, fontsize=11)
    ax2.grid(True, alpha=0.3, axis='y')
    ax2.tick_params(labelsize=10)
    
    # 在柱状图上添加数值标签
    for i, (bar, value) in enumerate(zip(bars2, energies)):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{value:.3f}J', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    # 设置y轴范围，留出空间显示标签
    ax2.set_ylim(0, max(energies) * 1.15)
    
    # 调整子图间距
    plt.tight_layout()
    
    return fig

def print_performance_summary():
    """打印性能总结"""
    print("=== 算法性能对比总结 ===")
    print()
    
    algorithms = ['MADDPG', 'SAC', 'MaxFlowMinCut']
    delays = [6.14, 1.41, 1.39]
    energies = [0.925, 0.270, 0.4775]
    
    # 延迟分析
    print("延迟性能排名:")
    delay_ranking = sorted(zip(algorithms, delays), key=lambda x: x[1])
    for i, (algo, delay) in enumerate(delay_ranking, 1):
        print(f"  {i}. {algo}: {delay:.2f}s")
    
    print()
    
    # 能耗分析
    print("能耗性能排名:")
    energy_ranking = sorted(zip(algorithms, energies), key=lambda x: x[1])
    for i, (algo, energy) in enumerate(energy_ranking, 1):
        print(f"  {i}. {algo}: {energy:.3f}J")
    
    print()
    
    # 性能改进分析
    print("相对于MADDPG的性能改进:")
    maddpg_delay, maddpg_energy = 6.14, 0.925
    
    for algo, delay, energy in zip(algorithms[1:], delays[1:], energies[1:]):
        delay_improvement = (maddpg_delay - delay) / maddpg_delay * 100
        energy_improvement = (maddpg_energy - energy) / maddpg_energy * 100
        print(f"  {algo}:")
        print(f"    延迟改进: {delay_improvement:.1f}%")
        print(f"    能耗改进: {energy_improvement:.1f}%")
    
    print()
    
    # 综合评价
    print("综合评价:")
    print("  • SAC在延迟和能耗方面都表现最优")
    print("  • MaxFlowMinCut在延迟方面与SAC接近，但能耗较高")
    print("  • MADDPG在两个指标上都表现较差")

def create_detailed_comparison():
    """创建详细的性能对比分析"""
    
    algorithms = ['MADDPG', 'SAC', 'MaxFlowMinCut']
    delays = [6.14, 1.41, 1.39]
    energies = [0.925, 0.270, 0.4775]
    colors = ['#d62728', '#1f77b4', '#ffcc00']
    
    # 创建更详细的对比图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('算法性能详细对比分析', fontsize=16, fontweight='bold')
    
    # 1. 延迟对比 (左上)
    ax1 = axes[0, 0]
    bars1 = ax1.bar(algorithms, delays, color=colors, alpha=0.8, edgecolor='black')
    ax1.set_title('延迟对比', fontsize=14, fontweight='bold')
    ax1.set_ylabel('延迟 (s)', fontsize=12)
    ax1.grid(True, alpha=0.3, axis='y')
    
    for bar, value in zip(bars1, delays):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{value:.2f}s', ha='center', va='bottom', fontweight='bold')
    
    # 2. 能耗对比 (右上)
    ax2 = axes[0, 1]
    bars2 = ax2.bar(algorithms, energies, color=colors, alpha=0.8, edgecolor='black')
    ax2.set_title('能耗对比', fontsize=14, fontweight='bold')
    ax2.set_ylabel('能耗 (J)', fontsize=12)
    ax2.grid(True, alpha=0.3, axis='y')
    
    for bar, value in zip(bars2, energies):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                f'{value:.3f}J', ha='center', va='bottom', fontweight='bold')
    
    # 3. 相对性能改进 (左下)
    ax3 = axes[1, 0]
    maddpg_delay, maddpg_energy = delays[0], energies[0]
    
    delay_improvements = [(maddpg_delay - d) / maddpg_delay * 100 for d in delays[1:]]
    energy_improvements = [(maddpg_energy - e) / maddpg_energy * 100 for e in energies[1:]]
    
    x = np.arange(len(algorithms[1:]))
    width = 0.35
    
    bars3_1 = ax3.bar(x - width/2, delay_improvements, width, label='延迟改进', 
                     color='lightcoral', alpha=0.8, edgecolor='black')
    bars3_2 = ax3.bar(x + width/2, energy_improvements, width, label='能耗改进', 
                     color='lightblue', alpha=0.8, edgecolor='black')
    
    ax3.set_title('相对于MADDPG的性能改进', fontsize=14, fontweight='bold')
    ax3.set_ylabel('改进百分比 (%)', fontsize=12)
    ax3.set_xticks(x)
    ax3.set_xticklabels(algorithms[1:])
    ax3.legend()
    ax3.grid(True, alpha=0.3, axis='y')
    
    # 添加数值标签
    for bars in [bars3_1, bars3_2]:
        for bar in bars:
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{height:.1f}%', ha='center', va='bottom', fontsize=9)
    
    # 4. 综合性能雷达图 (右下)
    ax4 = axes[1, 1]
    
    # 将指标标准化 (越小越好的指标转换为越大越好)
    max_delay = max(delays)
    max_energy = max(energies)
    
    normalized_delays = [(max_delay - d) / max_delay * 100 for d in delays]
    normalized_energies = [(max_energy - e) / max_energy * 100 for e in energies]
    
    x = np.arange(len(algorithms))
    width = 0.35
    
    bars4_1 = ax4.bar(x - width/2, normalized_delays, width, label='延迟性能', 
                     color='orange', alpha=0.8, edgecolor='black')
    bars4_2 = ax4.bar(x + width/2, normalized_energies, width, label='能耗性能', 
                     color='green', alpha=0.8, edgecolor='black')
    
    ax4.set_title('标准化性能对比', fontsize=14, fontweight='bold')
    ax4.set_ylabel('性能得分', fontsize=12)
    ax4.set_xticks(x)
    ax4.set_xticklabels(algorithms)
    ax4.legend()
    ax4.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    return fig

def main():
    """主函数"""
    print("创建算法性能对比可视化图表...\n")
    
    # 打印性能总结
    print_performance_summary()
    
    # 创建基本对比图
    fig1 = create_algorithm_comparison()
    
    # 保存图表
    output_file1 = "algorithm_comparison.png"
    fig1.savefig(output_file1, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"基本对比图已保存到: {output_file1}")
    
    # 创建详细对比图
    fig2 = create_detailed_comparison()
    
    # 保存详细图表
    output_file2 = "detailed_algorithm_comparison.png"
    fig2.savefig(output_file2, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"详细对比图已保存到: {output_file2}")
    
    # 显示图表
    plt.show()

if __name__ == "__main__":
    main()
