# Core dependencies for Heterogeneous Multi-Edge Server DNN Inference Optimization Framework

# Deep Learning
torch>=1.12.0
torchvision>=0.13.0

# Numerical Computing
numpy>=1.21.0
scipy>=1.7.0
pandas>=1.3.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# Machine Learning
scikit-learn>=1.0.0

# Utilities
tqdm>=4.62.0
argparse
json5
pyyaml>=6.0

# Optional: For parallel computing (if needed)
# ray>=1.13.0
# multiprocessing-logging>=0.3.0

# Development and Testing (optional)
# pytest>=6.0.0
# pytest-cov>=3.0.0
# black>=22.0.0
# flake8>=4.0.0
