"""
Adaptive Weight Mechanism for Battery-Aware Optimization
Implements battery-sensitive weight calculation for energy-delay trade-off
"""

import numpy as np
from typing import Dict, Any, List, Tuple
from config import config

class AdaptiveWeightMechanism:
    """
    Adaptive weight mechanism for battery-aware optimization
    Implements the sigmoid-based weight function from technical specification
    """
    
    def __init__(self, beta: float = None, theta: float = None):
        """
        Initialize adaptive weight mechanism
        
        Args:
            beta: Sensitivity parameter (uses config default if None)
            theta: Threshold parameter (uses config default if None)
        """
        self.beta = beta if beta is not None else config.optimization.beta
        self.theta = theta if theta is not None else config.optimization.theta
        
    def calculate_weight(self, battery_ratio: float) -> float:
        """
        Calculate adaptive weight based on battery level
        α_m = 1 / (1 + exp(-β(B_m/B_max - θ)))
        
        Args:
            battery_ratio: Current battery level as ratio of maximum (0-1)
            
        Returns:
            Adaptive weight (0-1) for energy-delay trade-off
        """
        # Clamp battery ratio to valid range
        battery_ratio = max(0.0, min(1.0, battery_ratio))
        
        # α_m = 1 / (1 + exp(-β(B_m/B_max - θ)))
        exponent = -self.beta * (battery_ratio - self.theta)
        weight = 1.0 / (1.0 + np.exp(exponent))
        
        return weight
    
    def calculate_multi_device_weights(self, battery_ratios: List[float]) -> List[float]:
        """
        Calculate adaptive weights for multiple devices
        
        Args:
            battery_ratios: List of battery ratios for each device
            
        Returns:
            List of adaptive weights for each device
        """
        return [self.calculate_weight(ratio) for ratio in battery_ratios]
    
    def get_weight_interpretation(self, weight: float) -> str:
        """
        Get human-readable interpretation of weight value
        
        Args:
            weight: Adaptive weight value (0-1)
            
        Returns:
            String interpretation of the weight
        """
        if weight >= 0.8:
            return "High Performance Priority (sufficient battery)"
        elif weight >= 0.6:
            return "Balanced Performance-Energy"
        elif weight >= 0.4:
            return "Moderate Energy Priority"
        elif weight >= 0.2:
            return "High Energy Priority (low battery)"
        else:
            return "Critical Energy Conservation (very low battery)"
    
    def calculate_objective_function(self, delay: float, energy: float, 
                                   weight: float, max_delay: float = None,
                                   max_energy: float = None) -> float:
        """
        Calculate weighted objective function value
        J_m = α_m * T_m + (1-α_m) * E_m
        
        Args:
            delay: Delay value in seconds
            energy: Energy value in Joules
            weight: Adaptive weight (0-1)
            max_delay: Maximum delay for normalization (optional)
            max_energy: Maximum energy for normalization (optional)
            
        Returns:
            Weighted objective function value
        """
        # Normalize values if max values provided
        if max_delay is not None and max_delay > 0:
            normalized_delay = delay / max_delay
        else:
            normalized_delay = delay
            
        if max_energy is not None and max_energy > 0:
            normalized_energy = energy / max_energy
        else:
            normalized_energy = energy
        
        # J_m = α_m * T_m + (1-α_m) * E_m
        objective = weight * normalized_delay + (1.0 - weight) * normalized_energy
        
        return objective
    
    def calculate_multi_device_objective(self, delays: List[float], energies: List[float],
                                       weights: List[float], max_delay: float = None,
                                       max_energy: float = None) -> Dict[str, Any]:
        """
        Calculate objective function for multiple devices
        J = (1/M) * Σ_m [α_m * T_m + (1-α_m) * E_m]
        
        Args:
            delays: List of delay values
            energies: List of energy values
            weights: List of adaptive weights
            max_delay: Maximum delay for normalization (optional)
            max_energy: Maximum energy for normalization (optional)
            
        Returns:
            Dictionary with objective values and statistics
        """
        if not (len(delays) == len(energies) == len(weights)):
            raise ValueError("All input lists must have the same length")
        
        device_objectives = []
        total_weighted_delay = 0.0
        total_weighted_energy = 0.0
        
        for delay, energy, weight in zip(delays, energies, weights):
            obj = self.calculate_objective_function(
                delay, energy, weight, max_delay, max_energy
            )
            device_objectives.append(obj)
            
            # Track weighted components
            if max_delay is not None and max_delay > 0:
                normalized_delay = delay / max_delay
            else:
                normalized_delay = delay
                
            if max_energy is not None and max_energy > 0:
                normalized_energy = energy / max_energy
            else:
                normalized_energy = energy
            
            total_weighted_delay += weight * normalized_delay
            total_weighted_energy += (1.0 - weight) * normalized_energy
        
        # Calculate system-wide objective
        num_devices = len(delays)
        system_objective = sum(device_objectives) / num_devices
        avg_weighted_delay = total_weighted_delay / num_devices
        avg_weighted_energy = total_weighted_energy / num_devices
        
        return {
            'device_objectives': device_objectives,
            'system_objective': system_objective,
            'avg_weighted_delay': avg_weighted_delay,
            'avg_weighted_energy': avg_weighted_energy,
            'total_objective': sum(device_objectives)
        }
    
    def analyze_weight_distribution(self, weights: List[float]) -> Dict[str, Any]:
        """
        Analyze the distribution of adaptive weights
        
        Args:
            weights: List of adaptive weight values
            
        Returns:
            Dictionary with weight distribution analysis
        """
        if not weights:
            return {}
        
        weights_array = np.array(weights)
        
        # Basic statistics
        mean_weight = np.mean(weights_array)
        std_weight = np.std(weights_array)
        min_weight = np.min(weights_array)
        max_weight = np.max(weights_array)
        
        # Categorize weights
        performance_priority = np.sum(weights_array >= 0.6)
        balanced = np.sum((weights_array >= 0.4) & (weights_array < 0.6))
        energy_priority = np.sum(weights_array < 0.4)
        
        # Calculate system tendency
        if mean_weight >= 0.6:
            system_tendency = "Performance-Oriented"
        elif mean_weight >= 0.4:
            system_tendency = "Balanced"
        else:
            system_tendency = "Energy-Oriented"
        
        return {
            'mean_weight': mean_weight,
            'std_weight': std_weight,
            'min_weight': min_weight,
            'max_weight': max_weight,
            'performance_priority_count': performance_priority,
            'balanced_count': balanced,
            'energy_priority_count': energy_priority,
            'system_tendency': system_tendency,
            'weight_range': max_weight - min_weight,
            'coefficient_of_variation': std_weight / mean_weight if mean_weight > 0 else 0.0
        }
    
    def get_battery_threshold_analysis(self) -> Dict[str, float]:
        """
        Analyze battery thresholds for different weight levels
        
        Returns:
            Dictionary with battery thresholds for different weight levels
        """
        # Calculate battery ratios for specific weight levels
        weight_levels = [0.1, 0.25, 0.5, 0.75, 0.9]
        battery_thresholds = {}
        
        for weight in weight_levels:
            # Solve for battery ratio: weight = 1 / (1 + exp(-β(B - θ)))
            # Rearranging: B = θ - ln((1/weight) - 1) / β
            if 0 < weight < 1:
                battery_ratio = self.theta - np.log((1.0 / weight) - 1.0) / self.beta
                battery_thresholds[f'weight_{weight}'] = max(0.0, min(1.0, battery_ratio))
        
        # Add special thresholds
        battery_thresholds['critical_energy'] = self.theta - np.log(9.0) / self.beta  # weight = 0.1
        battery_thresholds['balanced'] = self.theta  # weight = 0.5
        battery_thresholds['performance_focus'] = self.theta + np.log(9.0) / self.beta  # weight = 0.9
        
        return battery_thresholds
    
    def simulate_weight_curve(self, num_points: int = 100) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate weight curve for visualization
        
        Args:
            num_points: Number of points to generate
            
        Returns:
            Tuple of (battery_ratios, weights) arrays
        """
        battery_ratios = np.linspace(0.0, 1.0, num_points)
        weights = [self.calculate_weight(ratio) for ratio in battery_ratios]
        
        return battery_ratios, np.array(weights)
    
    def update_parameters(self, beta: float = None, theta: float = None):
        """
        Update mechanism parameters
        
        Args:
            beta: New sensitivity parameter (optional)
            theta: New threshold parameter (optional)
        """
        if beta is not None:
            self.beta = beta
        if theta is not None:
            self.theta = theta
    
    def get_info(self) -> Dict[str, Any]:
        """Get comprehensive information about the mechanism"""
        thresholds = self.get_battery_threshold_analysis()
        
        return {
            'beta': self.beta,
            'theta': self.theta,
            'mechanism_type': 'Sigmoid-based Adaptive Weight',
            'battery_thresholds': thresholds,
            'interpretation': {
                'beta': f"Sensitivity parameter (steepness of transition): {self.beta}",
                'theta': f"Threshold parameter (50% weight point): {self.theta}",
                'weight_range': "0 (energy priority) to 1 (performance priority)"
            }
        }
