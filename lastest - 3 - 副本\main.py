"""
Main Training and Evaluation Script for Heterogeneous Multi-Edge Server DNN Inference Optimization
Implements MADDPG training and baseline comparisons
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import torch
import argparse
import os
import json
from datetime import datetime
from typing import Dict, List, Any

from config import config
from environment import EdgeComputingEnvironment
from networks import MADDPG, SAC
from utils.visualization import VisualizationManager
from baselines import MaxFlowMinCutBaseline, FullLocalBaseline, FullOffloadBaseline

class Trainer:
    """A general trainer class that can handle different RL algorithms."""
    def __init__(self, env: EdgeComputingEnvironment, algo: str, save_dir: str = "results"):
        self.env = env
        self.algo_name = algo.upper()
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
        if algo == 'maddpg':
            self.agent = MADDPG(
                num_agents=env.num_devices,
                state_dim=env.state_dim,
                action_dim=env.action_dim
            )
        elif algo == 'sac':
            self.agent = SAC(
                num_agents=env.num_devices,
                state_dim=env.state_dim,
                action_dim=env.action_dim
            )
        else:
            raise ValueError(f"Unknown algorithm: {algo}")
        
        self.training_stats = {
            'episode_rewards': [], 'episode_energies': [], 'episode_delays': [],
            'episode_accuracies': [], 'episode_violations': [], 'avg_battery_levels': []
        }
    
    def train(self, num_episodes: int, save_frequency: int = 100,
              eval_frequency: int = 50) -> Dict[str, Any]:
        """Train the selected RL algorithm."""
        
        print(f"Starting {self.algo_name} training for {num_episodes} episodes...")
        print(f"Environment: {self.env.num_devices} devices, {self.env.num_servers} servers")
        print(f"State dim: {self.env.state_dim}, Action dim: {self.env.action_dim}")
        
        for episode in range(num_episodes):
            states = self.env.reset()
            if hasattr(self.agent, 'reset_noise'):
                self.agent.reset_noise()

            episode_reward, episode_energy, episode_delay, episode_violations = 0.0, 0.0, 0.0, 0
            step_count = 0
            
            while step_count < self.env.max_steps:
                actions = self.agent.act(states)
                next_states, rewards, dones, info = self.env.step(actions)
                self.agent.step(states, actions, rewards, next_states, dones)
                
                episode_reward += sum(rewards)
                episode_energy += sum(info['execution_results']['device_energies'])
                episode_delay += sum(info['execution_results']['device_delays'])
                episode_violations += sum(v for viol in info['execution_results']['constraint_violations'] for v in viol.values())
                
                states = next_states
                step_count += 1
                if all(dones): break
            
            if hasattr(self.agent, 'update_noise_scale'):
                self.agent.update_noise_scale(episode, num_episodes)

            self._collect_stats(episode, episode_reward, episode_energy, episode_delay, episode_violations)
            
            if (episode + 1) % 10 == 0:
                print(f"Episode {episode + 1}/{num_episodes}: "
                      f"Reward={episode_reward:.2f}, Energy={episode_energy:.2f}, "
                      f"Delay={episode_delay:.4f}, Violations={episode_violations}")

            if (episode + 1) % save_frequency == 0:
                model_path = os.path.join(self.save_dir, f"{self.algo_name.lower()}_episode_{episode + 1}.pth")
                self.agent.save_models(model_path)
                print(f"Saved model at episode {episode + 1}")

        final_model_path = os.path.join(self.save_dir, f"{self.algo_name.lower()}_final.pth")
        self.agent.save_models(final_model_path)
        
        stats_path = os.path.join(self.save_dir, f"{self.algo_name.lower()}_training_stats.json")
        with open(stats_path, 'w') as f:
            json.dump(self.training_stats, f, indent=2)
        
        print(f"{self.algo_name} training completed!")
        return self.training_stats

    def _collect_stats(self, episode, reward, energy, delay, violations):
        self.training_stats['episode_rewards'].append(float(reward))
        self.training_stats['episode_energies'].append(float(energy))
        self.training_stats['episode_delays'].append(float(delay))
        self.training_stats['episode_violations'].append(int(violations))
        self.training_stats['avg_battery_levels'].append(float(np.mean([d.get_battery_ratio() for d in self.env.devices])))
        self.training_stats['episode_accuracies'].append(float(np.mean([t.achieved_accuracy for t in self.env.tasks])))
        
    def plot_training_curves(self):
        """Plot training curves."""
        viz = VisualizationManager(self.save_dir)
        # Assuming viz manager has a generic plot function or create one
        # For simplicity, let's adapt the existing one in main
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        stats = self.training_stats
        axes[0, 0].plot(stats['episode_rewards']); axes[0, 0].set_title('Episode Rewards')
        axes[0, 1].plot(stats['episode_energies']); axes[0, 1].set_title('Episode Energy (J)')
        axes[0, 2].plot(stats['episode_delays']); axes[0, 2].set_title('Episode Delay (s)')
        axes[1, 0].plot(stats['episode_accuracies']); axes[1, 0].set_title('Average Accuracy')
        axes[1, 1].plot(stats['avg_battery_levels']); axes[1, 1].set_title('Average Battery Ratio')
        axes[1, 2].plot(stats['episode_violations']); axes[1, 2].set_title('Constraint Violations')
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, f'{self.algo_name.lower()}_training_curves.png'))
        plt.show()


class RlPolicyWrapper:
    """A wrapper to make a trained RL agent conform to the evaluation policy interface."""
    def __init__(self, agent, name="RL_Agent"):
        self.agent = agent
        self.__class__.__name__ = name

    def get_actions(self, states: List[np.ndarray], **kwargs) -> List[np.ndarray]:
        # The baseline `get_actions` provides more kwargs, but RL agents only need states
        return self.agent.act(states, add_noise=False)

def evaluate_policy(policy, env: EdgeComputingEnvironment, num_episodes: int = 50) -> Dict[str, Any]:
    """General function to evaluate a policy."""
    print(f"\n--- Evaluating policy: {policy.__class__.__name__} ---")
    
    all_rewards, all_energies, all_delays, all_accuracies = [], [], [], []
    total_violations = {'accuracy': 0, 'delay': 0, 'battery': 0}
    num_steps_total = 0

    for _ in range(num_episodes):
        states = env.reset()
        episode_reward, episode_energy, episode_delay = 0, 0, 0
        step, done = 0, False

        while not done:
            if hasattr(policy, 'agent'):  # MADDPG Wrapper
                actions = policy.get_actions(states)
                next_states, rewards, dones, info = env.step(actions)
            else:  # Baseline
                decoded_actions = policy.get_actions(
                    devices=env.devices, tasks=env.tasks,
                    servers=env.servers, network_conditions=env.network_conditions
                )
                next_states, rewards, dones, info = env.step_with_decoded_actions(decoded_actions)

            episode_reward += sum(rewards)
            episode_energy += sum(info['execution_results']['device_energies'])
            episode_delay += sum(info['execution_results']['device_delays'])

            for v in info['execution_results']['constraint_violations']:
                if v['accuracy']: total_violations['accuracy'] += 1
                if v['delay']: total_violations['delay'] += 1
                if v['battery']: total_violations['battery'] += 1
            
            states = next_states
            step += 1
            if step >= env.max_steps or all(dones):
                done = True

        all_rewards.append(episode_reward)
        all_energies.append(episode_energy)
        all_delays.append(episode_delay)
        all_accuracies.append(np.mean([t.achieved_accuracy for t in env.tasks]))
        num_steps_total += step * env.num_devices

    results = {
        'avg_reward': np.mean(all_rewards), 'avg_energy': np.mean(all_energies),
        'avg_delay': np.mean(all_delays), 'avg_accuracy': np.mean(all_accuracies),
        'violation_rate': {
            'accuracy': total_violations['accuracy'] / num_steps_total if num_steps_total else 0,
            'delay': total_violations['delay'] / num_steps_total if num_steps_total else 0,
            'battery': total_violations['battery'] / num_steps_total if num_steps_total else 0,
        }
    }
    print(f"Results: Avg Reward={results['avg_reward']:.2f}, Avg Energy={results['avg_energy']:.2f}, "
          f"Avg Delay={results['avg_delay']:.4f}, Avg Accuracy={results['avg_accuracy']:.3f}")
    return results

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='RL Training for Edge Computing Optimization')
    parser.add_argument('--algo', type=str, default='maddpg', choices=['maddpg', 'sac'], help='RL algorithm to use')
    parser.add_argument('--episodes', type=int, default=None, help='Number of training episodes')
    parser.add_argument('--save_dir', type=str, default='results', help='Directory to save results')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    
    args = parser.parse_args()
    
    # Set random seeds
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    
    # Create environment
    env = EdgeComputingEnvironment(random_seed=args.seed)
    
    # Determine number of episodes
    num_episodes = args.episodes
    if num_episodes is None:
        if args.algo == 'maddpg':
            num_episodes = config.maddpg.max_episodes
        else: # sac
            num_episodes = config.sac.max_episodes

    # Create and run trainer
    trainer = Trainer(env, args.algo, args.save_dir)
    training_stats = trainer.train(num_episodes)
    trainer.plot_training_curves()
    
    print("\n" + "="*50)
    print(" " * 15 + "STARTING BASELINE EVALUATION")
    print("="*50)

    # --- Run and Compare Baselines ---
    trained_rl_policy = RlPolicyWrapper(trainer.agent, name=args.algo.upper())
    
    baselines = {
        args.algo.upper() + " (Trained)": trained_rl_policy,
        "Max-Flow Min-Cut": MaxFlowMinCutBaseline(),
        "Full Local": FullLocalBaseline(),
        "Full Offload": FullOffloadBaseline()
    }

    baseline_results = {}
    for name, policy in baselines.items():
        baseline_results[name] = evaluate_policy(policy, env)

    # --- Visualize Comparison ---
    viz = VisualizationManager(save_dir=args.save_dir)
    viz.plot_accuracy_performance_tradeoff(baseline_results, save_name="baseline_comparison.png")
    
    print(f"\nTraining and evaluation completed! Results saved in {args.save_dir}")

if __name__ == "__main__":
    main()
