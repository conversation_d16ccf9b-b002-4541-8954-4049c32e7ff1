"""
Delay Calculation Model for Heterogeneous Multi-Edge Server DNN Inference
Implements comprehensive delay calculations based on technical specifications
"""

import numpy as np
from typing import Dict, Any, List, Tuple
from config import config

class DelayModel:
    """
    Delay calculation model implementing the mathematical formulations
    from the technical specification document
    """
    
    def __init__(self):
        """Initialize delay model with configuration parameters"""
        self.device_config = config.device
        self.server_config = config.server
        self.network_config = config.network
        self.optimization_config = config.optimization
        
    def calculate_local_delay(self, local_flops: float, device_frequency: float, 
                            g_device: float) -> float:
        """
        Calculate local processing delay
        T_local = C_local / (f_m * g_m)
        
        Args:
            local_flops: Number of FLOPs for local computation
            device_frequency: Device frequency in Hz
            g_device: Device computing capability (FLOPS per cycle)
            
        Returns:
            Local processing delay in seconds
        """
        if local_flops <= 0 or device_frequency <= 0 or g_device <= 0:
            return 0.0
        
        # T_local = C_local / (f_m * g_m)
        delay = local_flops / (device_frequency * g_device)
        
        return delay
    
    def calculate_communication_delay(self, data_size_bits: float, 
                                    transmission_rate: float) -> float:
        """
        Calculate communication transmission delay
        T_comm = D_tx / R_tx
        
        Args:
            data_size_bits: Data size in bits
            transmission_rate: Transmission rate in bps
            
        Returns:
            Communication delay in seconds
        """
        if data_size_bits <= 0 or transmission_rate <= 0:
            return 0.0
        
        # T_comm = D_tx / R_tx
        delay = data_size_bits / transmission_rate
        
        return delay
    
    def calculate_edge_delay(self, edge_flops: float, server_frequency: float, 
                           g_server: float) -> float:
        """
        Calculate edge processing delay
        T_edge = C_edge / (f_server * g_server)
        
        Args:
            edge_flops: Number of FLOPs for edge computation
            server_frequency: Server frequency in Hz
            g_server: Server computing capability (FLOPS per cycle)
            
        Returns:
            Edge processing delay in seconds
        """
        if edge_flops <= 0 or server_frequency <= 0 or g_server <= 0:
            return 0.0
        
        # T_edge = C_edge / (f_server * g_server)
        delay = edge_flops / (server_frequency * g_server)
        
        return delay
    
    def calculate_total_delay(self, local_flops: float, edge_flops: float,
                            data_size_bits: float, device_frequency: float,
                            server_frequency: float, transmission_rate: float,
                            g_device: float = None, g_server: float = None) -> Dict[str, float]:
        """
        Calculate total end-to-end delay
        T_total = T_local + T_comm + T_edge
        
        Args:
            local_flops: FLOPs for local computation
            edge_flops: FLOPs for edge computation
            data_size_bits: Data size for transmission in bits
            device_frequency: Device frequency in Hz
            server_frequency: Server frequency in Hz
            transmission_rate: Transmission rate in bps
            g_device: Device computing capability (optional)
            g_server: Server computing capability (optional)
            
        Returns:
            Dictionary with delay breakdown and total
        """
        if g_device is None:
            g_device = self.device_config.g_device
        if g_server is None:
            g_server = self.server_config.g_server
        
        # Calculate delay components
        local_delay = self.calculate_local_delay(local_flops, device_frequency, g_device)
        comm_delay = self.calculate_communication_delay(data_size_bits, transmission_rate)
        edge_delay = self.calculate_edge_delay(edge_flops, server_frequency, g_server)
        
        total_delay = local_delay + comm_delay + edge_delay
        
        return {
            'local_delay': local_delay,
            'communication_delay': comm_delay,
            'edge_delay': edge_delay,
            'total_delay': total_delay
        }
    
    def calculate_multi_device_delays(self, device_states: list, task_states: list,
                                    server_states: list, network_states: list) -> Dict[str, Any]:
        """
        Calculate delays for multiple devices
        
        Args:
            device_states: List of device state dictionaries
            task_states: List of task state dictionaries
            server_states: List of server state dictionaries
            network_states: List of network state dictionaries
            
        Returns:
            Dictionary with per-device delays and statistics
        """
        device_delays = []
        delay_breakdown = {
            'total_local': 0.0,
            'total_communication': 0.0,
            'total_edge': 0.0
        }
        
        for i, (device, task, server, network) in enumerate(
            zip(device_states, task_states, server_states, network_states)
        ):
            # Extract parameters
            local_flops = task.get('local_flops', 0.0)
            edge_flops = task.get('edge_flops', 0.0)
            data_size_bits = task.get('transmission_data_size', 0)
            device_frequency = device.get('frequency_hz', self.device_config.f_min)
            server_frequency = server.get('frequency_hz', self.server_config.f_server_min)
            transmission_rate = network.get('transmission_rate', self.network_config.rate_min)
            
            # Calculate delay for this device
            delay_result = self.calculate_total_delay(
                local_flops, edge_flops, data_size_bits,
                device_frequency, server_frequency, transmission_rate
            )
            
            device_delays.append(delay_result['total_delay'])
            
            # Update breakdown
            delay_breakdown['total_local'] += delay_result['local_delay']
            delay_breakdown['total_communication'] += delay_result['communication_delay']
            delay_breakdown['total_edge'] += delay_result['edge_delay']
        
        # Calculate statistics
        total_delays = sum(device_delays)
        avg_delay = total_delays / len(device_delays) if device_delays else 0.0
        max_delay = max(device_delays) if device_delays else 0.0
        min_delay = min(device_delays) if device_delays else 0.0
        
        return {
            'device_delays': device_delays,
            'total_system_delay': total_delays,
            'average_delay': avg_delay,
            'max_delay': max_delay,
            'min_delay': min_delay,
            'delay_breakdown': delay_breakdown
        }
    
    def validate_delay_constraints(self, device_delays: list, 
                                 max_delay: float = None) -> Dict[str, Any]:
        """
        Validate delay constraints for devices
        
        Args:
            device_delays: List of delay values in seconds
            max_delay: Maximum allowed delay (uses config default if None)
            
        Returns:
            Dictionary with constraint validation results
        """
        if max_delay is None:
            max_delay = self.optimization_config.t_max
        
        violations = []
        total_violations = 0
        
        for i, delay in enumerate(device_delays):
            if delay > max_delay:
                violations.append({
                    'device_id': i,
                    'actual_delay': delay,
                    'max_allowed_delay': max_delay,
                    'violation_amount': delay - max_delay
                })
                total_violations += 1
        
        return {
            'has_violations': len(violations) > 0,
            'violation_count': total_violations,
            'violations': violations,
            'violation_ratio': total_violations / len(device_delays) if device_delays else 0.0,
            'max_violation': max([v['violation_amount'] for v in violations]) if violations else 0.0
        }
    
    def get_delay_efficiency_metrics(self, delays: list, accuracies: list) -> Dict[str, float]:
        """
        Calculate delay efficiency metrics
        
        Args:
            delays: List of delay values in seconds
            accuracies: List of accuracy values (0-1)
            
        Returns:
            Dictionary with efficiency metrics
        """
        if not delays or not accuracies or len(delays) != len(accuracies):
            return {}
        
        # Delay per accuracy point
        delay_per_accuracy = [d / a if a > 0 else float('inf') for d, a in zip(delays, accuracies)]
        
        # Accuracy per second (throughput efficiency)
        accuracy_per_second = [a / d if d > 0 else 0.0 for d, a in zip(delays, accuracies)]
        
        # Average metrics
        avg_delay_per_accuracy = np.mean(delay_per_accuracy)
        avg_accuracy_per_second = np.mean(accuracy_per_second)
        
        return {
            'delay_per_accuracy': delay_per_accuracy,
            'accuracy_per_second': accuracy_per_second,
            'avg_delay_per_accuracy': avg_delay_per_accuracy,
            'avg_accuracy_per_second': avg_accuracy_per_second,
            'total_delay': sum(delays),
            'avg_delay': np.mean(delays),
            'delay_std': np.std(delays)
        }
    
    def calculate_delay_penalty(self, actual_delay: float, max_delay: float = None) -> float:
        """
        Calculate penalty for delay constraint violation
        
        Args:
            actual_delay: Actual delay in seconds
            max_delay: Maximum allowed delay (uses config default if None)
            
        Returns:
            Penalty value (0 if constraint satisfied, positive if violated)
        """
        if max_delay is None:
            max_delay = self.optimization_config.t_max
        
        if actual_delay > max_delay:
            violation_ratio = (actual_delay - max_delay) / max_delay
            return config.environment.delay_violation_penalty * violation_ratio
        
        return 0.0
    
    def optimize_frequency_for_delay(self, target_delay: float, local_flops: float,
                                   g_device: float = None) -> float:
        """
        Calculate required frequency to achieve target local delay
        
        Args:
            target_delay: Target delay in seconds
            local_flops: FLOPs for local computation
            g_device: Device computing capability (optional)
            
        Returns:
            Required frequency in Hz (clamped to valid range)
        """
        if g_device is None:
            g_device = self.device_config.g_device
        
        if target_delay <= 0 or local_flops <= 0:
            return self.device_config.f_max
        
        # f_required = C_local / (T_target * g_device)
        required_frequency = local_flops / (target_delay * g_device)
        
        # Clamp to valid frequency range
        required_frequency = max(self.device_config.f_min, 
                               min(self.device_config.f_max, required_frequency))
        
        return required_frequency
