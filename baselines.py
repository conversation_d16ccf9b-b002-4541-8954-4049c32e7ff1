import numpy as np
import networkx as nx
import random
import math
from typing import List, Dict, <PERSON>, <PERSON><PERSON>, Optional
from dataclasses import dataclass
from abc import ABC, abstractmethod

from config import config
from models import LocalDevice, EdgeInferenceTask, EdgeServer
from performance import DelayModel, EnergyModel, AdaptiveWeightMechanism
from resnet_layer_info import calculate_flops_up_to_layer, get_output_size_at_layer

@dataclass
class OptimizationConfig:
    """Configuration for hybrid optimization algorithms"""
    # Algorithm selection
    use_genetic_algorithm: bool = True
    use_graph_optimization: bool = True
    use_dynamic_frequency: bool = True

    # Genetic algorithm parameters
    population_size: int = 20
    generations: int = 10
    mutation_rate: float = 0.1
    crossover_rate: float = 0.8
    elite_ratio: float = 0.2

    # Frequency discretization
    frequency_levels: int = 5

    # Server selection parameters
    server_weight_load: float = 0.4
    server_weight_distance: float = 0.3
    server_weight_queue: float = 0.3

    # Optimization timeouts (seconds)
    max_optimization_time: float = 0.1

@dataclass
class OptimizationSolution:
    """Represents a complete optimization solution"""
    partition_point: int
    server_id: int
    frequency: float
    cost: float
    delay: float
    energy: float

class BaseOptimizer(ABC):
    """Abstract base class for optimization algorithms"""

    def __init__(self, delay_model: DelayModel, energy_model: EnergyModel):
        self.delay_model = delay_model
        self.energy_model = energy_model

    @abstractmethod
    def optimize(self, device: LocalDevice, task: EdgeInferenceTask,
                servers: List[EdgeServer], network_condition: Dict,
                alpha: float) -> OptimizationSolution:
        """Find optimal solution for given device-task pair"""
        pass

class CostCalculator:
    """Centralized cost calculation with caching"""

    def __init__(self, delay_model: DelayModel, energy_model: EnergyModel):
        self.delay_model = delay_model
        self.energy_model = energy_model
        self._cache = {}

    def calculate_cost(self, device: LocalDevice, task: EdgeInferenceTask,
                      server: EdgeServer, network_condition: Dict,
                      partition_point: int, frequency: float, alpha: float,
                      server_id: int) -> Tuple[float, float, float]:
        """
        Calculate total cost, delay, and energy for given configuration
        Returns: (total_cost, delay, energy)
        """
        # Create cache key
        cache_key = (
            id(device), id(task), id(server), partition_point,
            frequency, server_id, alpha
        )

        if cache_key in self._cache:
            return self._cache[cache_key]

        delay, energy = self._compute_delay_energy(
            device, task, server, network_condition,
            partition_point, frequency, server_id
        )

        total_cost = alpha * delay + (1 - alpha) * energy
        result = (total_cost, delay, energy)
        self._cache[cache_key] = result

        return result

    def _compute_delay_energy(self, device: LocalDevice, task: EdgeInferenceTask,
                             server: EdgeServer, network_condition: Dict,
                             partition_point: int, frequency: float,
                             server_id: int) -> Tuple[float, float]:
        """Compute delay and energy for given configuration"""

        # Full local execution
        if partition_point == 0:
            total_flops = calculate_flops_up_to_layer(task.early_exit_point)
            local_delay = self.delay_model.calculate_local_delay(total_flops, frequency, device.g_device)
            local_energy = self.energy_model.calculate_computation_energy(frequency, local_delay, device.kappa)
            return local_delay, local_energy

        # Full offload
        elif partition_point > task.early_exit_point:
            transmission_rate = network_condition['server_rates'][server_id]

            # Communication delay and energy for sending input data
            input_data_size = get_output_size_at_layer(0)
            comm_delay = self.delay_model.calculate_communication_delay(input_data_size, transmission_rate)
            comm_energy = self.energy_model.calculate_communication_energy(input_data_size, transmission_rate, device.p_tx)

            # Edge processing delay
            total_flops = calculate_flops_up_to_layer(task.early_exit_point)
            edge_delay = self.delay_model.calculate_edge_delay(total_flops, server.f_server, server.g_server)

            # Add queuing delay
            queuing_delay = server.calculate_queuing_delay()

            total_delay = comm_delay + edge_delay + queuing_delay
            return total_delay, comm_energy

        # Partial offload (hybrid execution)
        else:
            transmission_rate = network_condition['server_rates'][server_id]

            # Local computation cost
            local_flops = calculate_flops_up_to_layer(partition_point)
            local_delay = self.delay_model.calculate_local_delay(local_flops, frequency, device.g_device)
            local_energy = self.energy_model.calculate_computation_energy(frequency, local_delay, device.kappa)

            # Communication cost
            data_size = get_output_size_at_layer(partition_point)
            comm_delay = self.delay_model.calculate_communication_delay(data_size, transmission_rate)
            comm_energy = self.energy_model.calculate_communication_energy(data_size, transmission_rate, device.p_tx)

            # Edge computation cost
            edge_flops = calculate_flops_up_to_layer(task.early_exit_point) - local_flops
            edge_delay = self.delay_model.calculate_edge_delay(edge_flops, server.f_server, server.g_server)

            # Add queuing delay
            queuing_delay = server.calculate_queuing_delay()

            total_delay = local_delay + comm_delay + edge_delay + queuing_delay
            total_energy = local_energy + comm_energy

            return total_delay, total_energy

    def clear_cache(self):
        """Clear the cost calculation cache"""
        self._cache.clear()

class ServerSelector:
    """Advanced server selection with multiple criteria"""

    def __init__(self, config: OptimizationConfig):
        self.config = config

    def select_best_servers(self, device: LocalDevice, servers: List[EdgeServer],
                           network_condition: Dict, top_k: int = 3) -> List[int]:
        """
        Select top-k best servers based on multiple criteria
        Returns list of server indices sorted by preference
        """
        device_x, device_y = device.get_position()
        server_scores = []

        for i, server in enumerate(servers):
            server_x, server_y = server.get_position()
            distance = device.calculate_distance_to(server_x, server_y)

            # Normalize factors
            load_factor = server.get_total_load() / max(server.max_queue_size, 1)
            distance_factor = min(distance / config.physical.max_distance, 1.0)
            queue_factor = server.get_queue_length() / max(server.max_queue_size, 1)

            # Get transmission rate quality
            transmission_rate = network_condition['server_rates'][i]
            max_possible_rate = max(network_condition['server_rates'])
            rate_factor = 1.0 - (transmission_rate / max(max_possible_rate, 1))

            # Combined score (lower is better)
            score = (self.config.server_weight_load * load_factor +
                    self.config.server_weight_distance * distance_factor +
                    self.config.server_weight_queue * queue_factor +
                    0.2 * rate_factor)  # Add rate quality factor

            server_scores.append((score, i))

        # Sort by score and return top-k server indices
        server_scores.sort(key=lambda x: x[0])
        return [idx for _, idx in server_scores[:top_k]]

class FrequencyManager:
    """Dynamic frequency management based on device state"""

    def __init__(self, config: OptimizationConfig):
        self.config = config

    def get_frequency_candidates(self, device: LocalDevice) -> List[float]:
        """
        Get candidate frequencies based on device state and battery level
        """
        battery_ratio = device.current_battery / device.max_battery

        # Adjust frequency range based on battery level
        if battery_ratio < 0.2:  # Critical battery
            # Use lower frequencies to save energy
            f_min = device.f_min
            f_max = device.f_min + 0.3 * (device.f_max - device.f_min)
        elif battery_ratio < 0.5:  # Low battery
            # Use moderate frequencies
            f_min = device.f_min
            f_max = device.f_min + 0.7 * (device.f_max - device.f_min)
        else:  # Good battery
            # Use full frequency range
            f_min = device.f_min
            f_max = device.f_max

        # Generate frequency levels
        frequencies = []
        for i in range(self.config.frequency_levels):
            freq = f_min + (f_max - f_min) * i / (self.config.frequency_levels - 1)
            frequencies.append(freq)

        return frequencies

    def get_optimal_frequency(self, device: LocalDevice, task: EdgeInferenceTask,
                             partition_point: int, alpha: float) -> float:
        """
        Get optimal frequency for given partition point using energy-delay tradeoff
        """
        frequencies = self.get_frequency_candidates(device)

        if partition_point == 0:  # Full local execution
            # For local execution, consider energy-delay tradeoff
            local_flops = calculate_flops_up_to_layer(task.early_exit_point)

            best_freq = frequencies[0]
            best_cost = float('inf')

            for freq in frequencies:
                # Calculate delay and energy for this frequency
                delay = local_flops / (freq * device.g_device)
                energy = device.kappa * (freq / 1e9) ** 3 * delay

                cost = alpha * delay + (1 - alpha) * energy
                if cost < best_cost:
                    best_cost = cost
                    best_freq = freq

            return best_freq
        else:
            # For offloading, local computation is minimal, use moderate frequency
            return frequencies[len(frequencies) // 2]

class GreedyOptimizer(BaseOptimizer):
    """Fast greedy optimization algorithm"""

    def __init__(self, delay_model: DelayModel, energy_model: EnergyModel,
                 config: OptimizationConfig):
        super().__init__(delay_model, energy_model)
        self.config = config
        self.cost_calculator = CostCalculator(delay_model, energy_model)
        self.server_selector = ServerSelector(config)
        self.frequency_manager = FrequencyManager(config)

    def optimize(self, device: LocalDevice, task: EdgeInferenceTask,
                servers: List[EdgeServer], network_condition: Dict,
                alpha: float) -> OptimizationSolution:
        """
        Greedy optimization: select best server first, then optimize partition and frequency
        """
        # Step 1: Select top servers
        top_servers = self.server_selector.select_best_servers(
            device, servers, network_condition, top_k=2
        )

        best_solution = None
        best_cost = float('inf')

        # Step 2: For each top server, find best partition and frequency
        for server_id in top_servers:
            server = servers[server_id]

            # Get valid partition points
            partition_points = [0] + [p for p in task.valid_partition_points
                                    if p <= task.early_exit_point]
            partition_points = sorted(list(set(partition_points)))
            partition_points.append(task.early_exit_point + 1)  # Full offload

            # Step 3: For each partition point, find optimal frequency
            for partition_point in partition_points:
                if partition_point == 0:
                    # Full local: optimize frequency carefully
                    frequencies = self.frequency_manager.get_frequency_candidates(device)
                    for frequency in frequencies:
                        cost, delay, energy = self.cost_calculator.calculate_cost(
                            device, task, server, network_condition,
                            partition_point, frequency, alpha, server_id
                        )

                        if cost < best_cost:
                            best_cost = cost
                            best_solution = OptimizationSolution(
                                partition_point, server_id, frequency, cost, delay, energy
                            )
                else:
                    # Offloading: use adaptive frequency
                    frequency = self.frequency_manager.get_optimal_frequency(
                        device, task, partition_point, alpha
                    )

                    cost, delay, energy = self.cost_calculator.calculate_cost(
                        device, task, server, network_condition,
                        partition_point, frequency, alpha, server_id
                    )

                    if cost < best_cost:
                        best_cost = cost
                        best_solution = OptimizationSolution(
                            partition_point, server_id, frequency, cost, delay, energy
                        )

        return best_solution if best_solution else OptimizationSolution(
            0, 0, device.f_min, float('inf'), float('inf'), float('inf')
        )

class GraphOptimizer(BaseOptimizer):
    """Graph-based optimization using shortest path algorithms"""

    def __init__(self, delay_model: DelayModel, energy_model: EnergyModel,
                 config: OptimizationConfig):
        super().__init__(delay_model, energy_model)
        self.config = config
        self.cost_calculator = CostCalculator(delay_model, energy_model)
        self.server_selector = ServerSelector(config)
        self.frequency_manager = FrequencyManager(config)

    def optimize(self, device: LocalDevice, task: EdgeInferenceTask,
                servers: List[EdgeServer], network_condition: Dict,
                alpha: float) -> OptimizationSolution:
        """
        Graph-based optimization: model as shortest path problem
        """
        # Create directed graph
        G = nx.DiGraph()

        # Get candidates
        top_servers = self.server_selector.select_best_servers(
            device, servers, network_condition, top_k=len(servers)
        )
        partition_points = [0] + [p for p in task.valid_partition_points
                                if p <= task.early_exit_point]
        partition_points = sorted(list(set(partition_points)))
        partition_points.append(task.early_exit_point + 1)

        frequencies = self.frequency_manager.get_frequency_candidates(device)

        # Add source and sink nodes
        source = "source"
        sink = "sink"
        G.add_node(source)
        G.add_node(sink)

        # Add configuration nodes
        config_nodes = []
        for server_id in top_servers:
            for partition_point in partition_points:
                for frequency in frequencies:
                    node_id = f"s{server_id}_p{partition_point}_f{frequency:.0f}"
                    config_nodes.append((node_id, server_id, partition_point, frequency))
                    G.add_node(node_id)

        # Add edges from source to all configurations
        for node_id, server_id, partition_point, frequency in config_nodes:
            server = servers[server_id]
            cost, delay, energy = self.cost_calculator.calculate_cost(
                device, task, server, network_condition,
                partition_point, frequency, alpha, server_id
            )
            G.add_edge(source, node_id, weight=cost)

        # Add edges from all configurations to sink
        for node_id, _, _, _ in config_nodes:
            G.add_edge(node_id, sink, weight=0)

        try:
            # Find shortest path
            path = nx.shortest_path(G, source, sink, weight='weight')
            if len(path) >= 3:  # source -> config -> sink
                config_node = path[1]
                # Parse configuration from node name
                parts = config_node.split('_')
                server_id = int(parts[0][1:])
                partition_point = int(parts[1][1:])
                frequency = float(parts[2][1:])

                server = servers[server_id]
                cost, delay, energy = self.cost_calculator.calculate_cost(
                    device, task, server, network_condition,
                    partition_point, frequency, alpha, server_id
                )

                return OptimizationSolution(
                    partition_point, server_id, frequency, cost, delay, energy
                )
        except nx.NetworkXNoPath:
            pass

        # Fallback to greedy if graph method fails
        greedy_optimizer = GreedyOptimizer(self.delay_model, self.energy_model, self.config)
        return greedy_optimizer.optimize(device, task, servers, network_condition, alpha)

class GeneticOptimizer(BaseOptimizer):
    """Genetic algorithm for global optimization"""

    def __init__(self, delay_model: DelayModel, energy_model: EnergyModel,
                 config: OptimizationConfig):
        super().__init__(delay_model, energy_model)
        self.config = config
        self.cost_calculator = CostCalculator(delay_model, energy_model)
        self.server_selector = ServerSelector(config)
        self.frequency_manager = FrequencyManager(config)

    def optimize(self, device: LocalDevice, task: EdgeInferenceTask,
                servers: List[EdgeServer], network_condition: Dict,
                alpha: float) -> OptimizationSolution:
        """
        Genetic algorithm optimization
        Individual encoding: [server_id, partition_point, frequency_level]
        """
        # Get search space
        top_servers = self.server_selector.select_best_servers(
            device, servers, network_condition, top_k=len(servers)
        )
        partition_points = [0] + [p for p in task.valid_partition_points
                                if p <= task.early_exit_point]
        partition_points = sorted(list(set(partition_points)))
        partition_points.append(task.early_exit_point + 1)
        frequencies = self.frequency_manager.get_frequency_candidates(device)

        # Initialize population
        population = self._initialize_population(top_servers, partition_points, frequencies)

        best_solution = None
        best_fitness = float('-inf')

        # Evolution loop
        for generation in range(self.config.generations):
            # Evaluate fitness
            fitness_scores = []
            for individual in population:
                fitness = self._evaluate_fitness(
                    individual, device, task, servers, network_condition,
                    alpha, partition_points, frequencies
                )
                fitness_scores.append(fitness)

                if fitness > best_fitness:
                    best_fitness = fitness
                    best_solution = self._decode_individual(
                        individual, device, task, servers, network_condition,
                        alpha, partition_points, frequencies
                    )

            # Selection, crossover, and mutation
            population = self._evolve_population(population, fitness_scores)

        return best_solution if best_solution else OptimizationSolution(
            0, 0, device.f_min, float('inf'), float('inf'), float('inf')
        )

    def _initialize_population(self, servers: List[int], partition_points: List[int],
                              frequencies: List[float]) -> List[List[int]]:
        """Initialize random population"""
        population = []
        for _ in range(self.config.population_size):
            individual = [
                random.choice(range(len(servers))),  # server index
                random.choice(range(len(partition_points))),  # partition index
                random.choice(range(len(frequencies)))  # frequency index
            ]
            population.append(individual)
        return population

    def _evaluate_fitness(self, individual: List[int], device: LocalDevice,
                         task: EdgeInferenceTask, servers: List[EdgeServer],
                         network_condition: Dict, alpha: float,
                         partition_points: List[int], frequencies: List[float]) -> float:
        """Evaluate fitness of individual (higher is better)"""
        try:
            server_idx, partition_idx, freq_idx = individual
            server_id = server_idx % len(servers)
            partition_point = partition_points[partition_idx % len(partition_points)]
            frequency = frequencies[freq_idx % len(frequencies)]

            server = servers[server_id]
            cost, _, _ = self.cost_calculator.calculate_cost(
                device, task, server, network_condition,
                partition_point, frequency, alpha, server_id
            )

            # Convert cost to fitness (lower cost = higher fitness)
            return -cost if cost != float('inf') else float('-inf')
        except:
            return float('-inf')

    def _decode_individual(self, individual: List[int], device: LocalDevice,
                          task: EdgeInferenceTask, servers: List[EdgeServer],
                          network_condition: Dict, alpha: float,
                          partition_points: List[int], frequencies: List[float]) -> OptimizationSolution:
        """Decode individual to solution"""
        server_idx, partition_idx, freq_idx = individual
        server_id = server_idx % len(servers)
        partition_point = partition_points[partition_idx % len(partition_points)]
        frequency = frequencies[freq_idx % len(frequencies)]

        server = servers[server_id]
        cost, delay, energy = self.cost_calculator.calculate_cost(
            device, task, server, network_condition,
            partition_point, frequency, alpha, server_id
        )

        return OptimizationSolution(
            partition_point, server_id, frequency, cost, delay, energy
        )

    def _evolve_population(self, population: List[List[int]],
                          fitness_scores: List[float]) -> List[List[int]]:
        """Evolve population using selection, crossover, and mutation"""
        # Sort by fitness
        sorted_pop = sorted(zip(population, fitness_scores),
                           key=lambda x: x[1], reverse=True)

        # Elite selection
        elite_size = int(self.config.elite_ratio * len(population))
        new_population = [ind for ind, _ in sorted_pop[:elite_size]]

        # Generate offspring
        while len(new_population) < self.config.population_size:
            # Tournament selection
            parent1 = self._tournament_selection(sorted_pop)
            parent2 = self._tournament_selection(sorted_pop)

            # Crossover
            if random.random() < self.config.crossover_rate:
                child1, child2 = self._crossover(parent1, parent2)
            else:
                child1, child2 = parent1[:], parent2[:]

            # Mutation
            if random.random() < self.config.mutation_rate:
                child1 = self._mutate(child1)
            if random.random() < self.config.mutation_rate:
                child2 = self._mutate(child2)

            new_population.extend([child1, child2])

        return new_population[:self.config.population_size]

    def _tournament_selection(self, sorted_pop: List[Tuple[List[int], float]]) -> List[int]:
        """Tournament selection"""
        tournament_size = 3
        tournament = random.sample(sorted_pop, min(tournament_size, len(sorted_pop)))
        return max(tournament, key=lambda x: x[1])[0]

    def _crossover(self, parent1: List[int], parent2: List[int]) -> Tuple[List[int], List[int]]:
        """Single-point crossover"""
        crossover_point = random.randint(1, len(parent1) - 1)
        child1 = parent1[:crossover_point] + parent2[crossover_point:]
        child2 = parent2[:crossover_point] + parent1[crossover_point:]
        return child1, child2

    def _mutate(self, individual: List[int]) -> List[int]:
        """Random mutation"""
        mutated = individual[:]
        gene_idx = random.randint(0, len(individual) - 1)
        if gene_idx == 0:  # server
            mutated[gene_idx] = random.randint(0, 10)  # Assume max 10 servers
        elif gene_idx == 1:  # partition
            mutated[gene_idx] = random.randint(0, 20)  # Assume max 20 partitions
        else:  # frequency
            mutated[gene_idx] = random.randint(0, self.config.frequency_levels - 1)
        return mutated

class HybridOptimizationBaseline:
    """
    Advanced hybrid optimization baseline that combines multiple algorithms:
    - Greedy optimization for fast solutions
    - Graph-based optimization using shortest path algorithms
    - Genetic algorithm for global optimization
    - Dynamic frequency management
    - Intelligent server selection

    This replaces the original MaxFlowMinCutBaseline with a more sophisticated
    approach that actually optimizes CPU frequency, server selection, and
    partition points simultaneously.
    """

    def __init__(self, optimization_config: Optional[OptimizationConfig] = None):
        self.delay_model = DelayModel()
        self.energy_model = EnergyModel()
        self.weight_mechanism = AdaptiveWeightMechanism()
        self.config = optimization_config or OptimizationConfig()

        # Initialize optimizers
        self.greedy_optimizer = GreedyOptimizer(
            self.delay_model, self.energy_model, self.config
        )

        if self.config.use_graph_optimization:
            self.graph_optimizer = GraphOptimizer(
                self.delay_model, self.energy_model, self.config
            )

        if self.config.use_genetic_algorithm:
            self.genetic_optimizer = GeneticOptimizer(
                self.delay_model, self.energy_model, self.config
            )

        # Performance tracking
        self.optimization_stats = {
            'greedy_calls': 0,
            'graph_calls': 0,
            'genetic_calls': 0,
            'total_time': 0.0
        }

    def get_actions(self, devices: List[LocalDevice], tasks: List[EdgeInferenceTask],
                    servers: List[EdgeServer], network_conditions: List[Dict]) -> List[Dict[str, Any]]:
        """
        Get optimized actions for all devices using hybrid optimization approach
        """
        import time
        start_time = time.time()

        actions = []
        for i in range(len(devices)):
            action = self._get_single_action(
                devices[i], tasks[i], servers, network_conditions[i]
            )
            actions.append(action)

        self.optimization_stats['total_time'] += time.time() - start_time
        return actions

    def _get_single_action(self, device: LocalDevice, task: EdgeInferenceTask,
                           servers: List[EdgeServer], network_condition: Dict) -> Dict[str, Any]:
        """
        Intelligent single device optimization using adaptive algorithm selection
        """
        alpha = device.get_adaptive_weight()
        battery_ratio = device.current_battery / device.max_battery

        # Select optimization algorithm based on system state
        solution = self._select_and_run_optimizer(
            device, task, servers, network_condition, alpha, battery_ratio
        )

        return {
            'partition_point': solution.partition_point,
            'server_id': solution.server_id,
            'frequency': solution.frequency
        }

    def _select_and_run_optimizer(self, device: LocalDevice, task: EdgeInferenceTask,
                                 servers: List[EdgeServer], network_condition: Dict,
                                 alpha: float, battery_ratio: float) -> OptimizationSolution:
        """
        Intelligently select and run the most appropriate optimizer
        """
        # Decision logic for algorithm selection
        num_servers = len(servers)
        num_partitions = len([p for p in task.valid_partition_points if p <= task.early_exit_point]) + 2
        search_space_size = num_servers * num_partitions * self.config.frequency_levels

        # For small search spaces or critical battery, use greedy (fastest)
        if search_space_size <= 50 or battery_ratio < 0.2:
            self.optimization_stats['greedy_calls'] += 1
            return self.greedy_optimizer.optimize(
                device, task, servers, network_condition, alpha
            )

        # For medium search spaces, try graph optimization first
        elif search_space_size <= 200 and self.config.use_graph_optimization:
            self.optimization_stats['graph_calls'] += 1
            try:
                return self.graph_optimizer.optimize(
                    device, task, servers, network_condition, alpha
                )
            except Exception:
                # Fallback to greedy if graph optimization fails
                self.optimization_stats['greedy_calls'] += 1
                return self.greedy_optimizer.optimize(
                    device, task, servers, network_condition, alpha
                )

        # For large search spaces and good battery, use genetic algorithm
        elif self.config.use_genetic_algorithm and battery_ratio > 0.5:
            self.optimization_stats['genetic_calls'] += 1
            return self.genetic_optimizer.optimize(
                device, task, servers, network_condition, alpha
            )

        # Default fallback to greedy
        else:
            self.optimization_stats['greedy_calls'] += 1
            return self.greedy_optimizer.optimize(
                device, task, servers, network_condition, alpha
            )

    def get_optimization_stats(self) -> Dict[str, Any]:
        """Get optimization performance statistics"""
        total_calls = (self.optimization_stats['greedy_calls'] +
                      self.optimization_stats['graph_calls'] +
                      self.optimization_stats['genetic_calls'])

        if total_calls == 0:
            return self.optimization_stats

        stats = self.optimization_stats.copy()
        stats['avg_time_per_call'] = stats['total_time'] / total_calls
        stats['greedy_ratio'] = stats['greedy_calls'] / total_calls
        stats['graph_ratio'] = stats['graph_calls'] / total_calls
        stats['genetic_ratio'] = stats['genetic_calls'] / total_calls

        return stats

    def reset_stats(self):
        """Reset optimization statistics"""
        self.optimization_stats = {
            'greedy_calls': 0,
            'graph_calls': 0,
            'genetic_calls': 0,
            'total_time': 0.0
        }

# Renamed from MaxFlowMinCutBaseline to reflect actual implementation
class GreedyOptimizationBaseline(HybridOptimizationBaseline):
    """
    Greedy optimization baseline algorithm
    Uses primarily greedy optimization with graph-based fallback

    Note: Previously named MaxFlowMinCutBaseline, but renamed to accurately
    reflect the actual implementation (greedy optimization, not max-flow min-cut)
    """

    def __init__(self):
        # Use configuration optimized for greedy approach
        super().__init__(OptimizationConfig(
            use_genetic_algorithm=False,  # Disable genetic for faster execution
            use_graph_optimization=True,  # Enable as fallback
            use_dynamic_frequency=True,
            generations=5,  # Reduced for faster execution
            population_size=10
        ))

# Keep the original MaxFlowMinCutBaseline name for backward compatibility
# but mark it as deprecated
class MaxFlowMinCutBaseline(GreedyOptimizationBaseline):
    """
    DEPRECATED: This class name is misleading as it doesn't implement max-flow min-cut.
    Use GreedyOptimizationBaseline instead.

    This is actually a greedy optimization algorithm with the following features:
    - Intelligent server selection based on load, distance, queue, and transmission rate
    - Dynamic frequency management based on battery level
    - Adaptive algorithm selection (primarily greedy)

    Kept for backward compatibility only.
    """

    def __init__(self):
        import warnings
        warnings.warn(
            "MaxFlowMinCutBaseline is deprecated and misleading. "
            "This algorithm actually uses greedy optimization, not max-flow min-cut. "
            "Use GreedyOptimizationBaseline instead.",
            DeprecationWarning,
            stacklevel=2
        )
        super().__init__()


class FullLocalBaseline:
    """Baseline that always executes tasks locally at the lowest frequency."""
    def get_actions(self, devices: List[LocalDevice], **kwargs) -> List[Dict[str, Any]]:
        return [{'partition_point': 0, 'server_id': 0, 'frequency': dev.f_min} for dev in devices]


class FullOffloadBaseline:
    """Baseline that always offloads tasks to the best server considering distance and load."""
    def get_actions(self, devices: List[LocalDevice], tasks: List[EdgeInferenceTask],
                    servers: List[EdgeServer], **kwargs) -> List[Dict[str, Any]]:
        actions = []
        for i in range(len(devices)):
            device = devices[i]
            device_x, device_y = device.get_position()

            # Calculate server scores based on load, distance, and queue status
            server_scores = []
            for server in servers:
                server_x, server_y = server.get_position()
                distance = device.calculate_distance_to(server_x, server_y)

                # Normalize factors
                load_factor = server.get_total_load() / server.max_queue_size  # 0-1
                distance_factor = min(distance / config.physical.max_distance, 1.0)  # 0-1
                queue_factor = server.get_queue_length() / server.max_queue_size  # 0-1

                # Combined score (lower is better)
                score = 0.4 * load_factor + 0.3 * distance_factor + 0.3 * queue_factor
                server_scores.append(score)

            server_id = np.argmin(server_scores) if server_scores else 0
            actions.append({
                'partition_point': tasks[i].early_exit_point + 1,
                'server_id': server_id,
                'frequency': devices[i].f_max
            })
        return actions
