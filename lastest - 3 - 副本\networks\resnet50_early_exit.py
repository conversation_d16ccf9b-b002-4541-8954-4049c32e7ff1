"""
ResNet-50 with Early Exit Implementation for CIFAR-10
Implements the network architecture with early exit branches based on analysis data
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple
from config import config
from resnet_layer_info import RESNET50_LAYERS, get_layer_info, get_conv_layers

class BasicBlock(nn.Module):
    """Basic residual block for ResNet"""
    expansion = 1
    
    def __init__(self, in_planes, planes, stride=1):
        super(BasicBlock, self).__init__()
        self.conv1 = nn.Conv2d(in_planes, planes, kernel_size=3, stride=stride, padding=1, bias=False)
        self.bn1 = nn.BatchNorm2d(planes)
        self.conv2 = nn.Conv2d(planes, planes, kernel_size=3, stride=1, padding=1, bias=False)
        self.bn2 = nn.BatchNorm2d(planes)
        
        self.shortcut = nn.Sequential()
        if stride != 1 or in_planes != self.expansion * planes:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_planes, self.expansion * planes, kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm2d(self.expansion * planes)
            )
    
    def forward(self, x):
        out = F.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        out += self.shortcut(x)
        out = F.relu(out)
        return out

class Bottleneck(nn.Module):
    """Bottleneck residual block for ResNet"""
    expansion = 4
    
    def __init__(self, in_planes, planes, stride=1):
        super(Bottleneck, self).__init__()
        self.conv1 = nn.Conv2d(in_planes, planes, kernel_size=1, bias=False)
        self.bn1 = nn.BatchNorm2d(planes)
        self.conv2 = nn.Conv2d(planes, planes, kernel_size=3, stride=stride, padding=1, bias=False)
        self.bn2 = nn.BatchNorm2d(planes)
        self.conv3 = nn.Conv2d(planes, self.expansion * planes, kernel_size=1, bias=False)
        self.bn3 = nn.BatchNorm2d(self.expansion * planes)
        
        self.shortcut = nn.Sequential()
        if stride != 1 or in_planes != self.expansion * planes:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_planes, self.expansion * planes, kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm2d(self.expansion * planes)
            )
    
    def forward(self, x):
        out = F.relu(self.bn1(self.conv1(x)))
        out = F.relu(self.bn2(self.conv2(out)))
        out = self.bn3(self.conv3(out))
        out += self.shortcut(x)
        out = F.relu(out)
        return out

class EarlyExitBranch(nn.Module):
    """Early exit branch for intermediate predictions"""
    
    def __init__(self, in_channels, num_classes=10):
        super(EarlyExitBranch, self).__init__()
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        self.flatten = nn.Flatten()
        self.fc = nn.Linear(in_channels, num_classes)
    
    def forward(self, x):
        out = self.avgpool(x)
        out = self.flatten(out)
        out = self.fc(out)
        return out

class ResNet50EarlyExit(nn.Module):
    """
    ResNet-50 with Early Exit branches for CIFAR-10
    Based on the layer analysis and accuracy data
    """
    
    def __init__(self, num_classes=10):
        super(ResNet50EarlyExit, self).__init__()
        self.num_classes = num_classes
        self.in_planes = 64
        
        # Initial convolution
        self.conv1 = nn.Conv2d(3, 64, kernel_size=3, stride=1, padding=1, bias=False)
        self.bn1 = nn.BatchNorm2d(64)
        
        # ResNet layers
        self.layer1 = self._make_layer(Bottleneck, 64, 3, stride=1)
        self.layer2 = self._make_layer(Bottleneck, 128, 4, stride=2)
        self.layer3 = self._make_layer(Bottleneck, 256, 6, stride=2)
        self.layer4 = self._make_layer(Bottleneck, 512, 3, stride=2)
        
        # Early exit branches based on accuracy analysis
        self.early_exit_1 = EarlyExitBranch(256, num_classes)  # After layer1 (73)
        self.early_exit_2 = EarlyExitBranch(512, num_classes)  # After layer2 (105)
        self.early_exit_3 = EarlyExitBranch(1024, num_classes) # After layer3[2] (135)
        self.early_exit_4 = EarlyExitBranch(1024, num_classes) # After layer3[5] (150)
        self.early_exit_5 = EarlyExitBranch(2048, num_classes) # After layer4[0] (166)
        
        # Final classifier
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        self.fc = nn.Linear(512 * Bottleneck.expansion, num_classes)
        
        # Track layer indices for partition points
        self.layer_indices = self._build_layer_index_map()
        self.early_exit_points = config.resnet.early_exit_points
        self.early_exit_accuracies = config.resnet.early_exit_accuracies
        
    def _make_layer(self, block, planes, num_blocks, stride):
        """Create a layer with multiple blocks"""
        strides = [stride] + [1] * (num_blocks - 1)
        layers = []
        for stride in strides:
            layers.append(block(self.in_planes, planes, stride))
            self.in_planes = planes * block.expansion
        return nn.Sequential(*layers)
    
    def _build_layer_index_map(self):
        """Build mapping between layer names and indices"""
        layer_map = {}
        idx = 0
        
        # Initial layers
        layer_map['conv1'] = idx
        idx += 1
        layer_map['bn1'] = idx
        idx += 1
        layer_map['relu'] = idx
        idx += 1
        
        # Layer blocks
        for layer_name in ['layer1', 'layer2', 'layer3', 'layer4']:
            layer_map[layer_name] = idx
            # Each layer contains multiple blocks, increment accordingly
            if layer_name == 'layer1':
                idx += 3 * 3  # 3 blocks, each with 3 conv layers
            elif layer_name == 'layer2':
                idx += 4 * 3  # 4 blocks
            elif layer_name == 'layer3':
                idx += 6 * 3  # 6 blocks
            elif layer_name == 'layer4':
                idx += 3 * 3  # 3 blocks
        
        return layer_map
    
    def forward(self, x, exit_point: str = "Full", partition_point: int = 0):
        """
        Forward pass with early exit and partition support
        
        Args:
            x: Input tensor
            exit_point: Early exit point name ("Exit_1", "Exit_2", etc., or "Full")
            partition_point: Layer index for partitioning (0 for full local)
            
        Returns:
            Output tensor or tuple of (output, intermediate_features)
        """
        # Initial convolution
        out = F.relu(self.bn1(self.conv1(x)))
        
        # Layer 1
        out = self.layer1(out)
        if exit_point == "Exit_1":
            return self.early_exit_1(out)
        
        # Layer 2
        out = self.layer2(out)
        if exit_point == "Exit_2":
            return self.early_exit_2(out)
        
        # Layer 3 (with intermediate exits)
        layer3_blocks = list(self.layer3.children())
        for i, block in enumerate(layer3_blocks):
            out = block(out)
            if exit_point == "Exit_3" and i == 2:  # After layer3[2]
                return self.early_exit_3(out)
            if exit_point == "Exit_4" and i == 5:  # After layer3[5]
                return self.early_exit_4(out)
        
        # Layer 4
        layer4_blocks = list(self.layer4.children())
        out = layer4_blocks[0](out)  # First block of layer4
        if exit_point == "Exit_5":
            return self.early_exit_5(out)
        
        # Complete layer 4
        for block in layer4_blocks[1:]:
            out = block(out)
        
        # Final classification
        out = self.avgpool(out)
        out = torch.flatten(out, 1)
        out = self.fc(out)
        
        return out
    
    def forward_with_partition(self, x, partition_point: int, exit_point: str = "Full"):
        """
        Forward pass with partitioning support for edge computing
        
        Args:
            x: Input tensor
            partition_point: Layer index where to partition (0 for full local)
            exit_point: Early exit point name
            
        Returns:
            Dictionary with local output, edge input, and metadata
        """
        if partition_point == 0:
            # Full local execution
            output = self.forward(x, exit_point)
            return {
                'local_output': output,
                'edge_input': None,
                'partition_point': partition_point,
                'exit_point': exit_point,
                'requires_edge': False
            }
        
        # Partial local execution up to partition point
        # This is a simplified implementation - in practice, you'd need
        # more sophisticated layer-by-layer execution control
        intermediate_features = self._forward_to_layer(x, partition_point)
        
        return {
            'local_output': None,
            'edge_input': intermediate_features,
            'partition_point': partition_point,
            'exit_point': exit_point,
            'requires_edge': True
        }
    
    def _forward_to_layer(self, x, target_layer: int):
        """Forward pass up to a specific layer index"""
        # This is a simplified implementation
        # In practice, you'd need detailed layer-by-layer control
        current_layer = 0
        out = x
        
        # Initial conv
        if current_layer < target_layer:
            out = F.relu(self.bn1(self.conv1(out)))
            current_layer += 3  # conv1 + bn1 + relu
        
        # Layer 1
        if current_layer < target_layer:
            out = self.layer1(out)
            current_layer += 9  # 3 blocks * 3 layers each
        
        # Continue for other layers...
        # This would need to be implemented in detail for production use
        
        return out
    
    def get_valid_partition_points(self, exit_point: str) -> List[int]:
        """
        Get valid partition points for a given exit point
        
        Args:
            exit_point: Early exit point name
            
        Returns:
            List of valid partition point indices (Conv2d layers only)
        """
        exit_layer = self.early_exit_points[exit_point]
        return config.get_valid_partitions_for_exit(exit_layer)
    
    def get_model_info(self) -> Dict:
        """Get comprehensive model information"""
        return {
            'model_name': 'ResNet-50 with Early Exits',
            'dataset': 'CIFAR-10',
            'num_classes': self.num_classes,
            'early_exit_points': self.early_exit_points,
            'early_exit_accuracies': self.early_exit_accuracies,
            'valid_partition_points': config.resnet.valid_partition_points,
            'total_layers': len(RESNET50_LAYERS)
        }
    
    def calculate_flops_to_layer(self, layer_idx: int) -> float:
        """Calculate FLOPs up to a specific layer"""
        from resnet_layer_info import calculate_flops_up_to_layer
        return calculate_flops_up_to_layer(layer_idx)
    
    def get_accuracy_for_exit(self, exit_point: str) -> float:
        """Get accuracy for a specific exit point"""
        return self.early_exit_accuracies.get(exit_point, 0.0)

def create_resnet50_early_exit(num_classes=10, pretrained=False):
    """
    Create ResNet-50 with early exits
    
    Args:
        num_classes: Number of output classes
        pretrained: Whether to load pretrained weights (not implemented)
        
    Returns:
        ResNet50EarlyExit model
    """
    model = ResNet50EarlyExit(num_classes)
    
    if pretrained:
        # In practice, you would load pretrained weights here
        print("Warning: Pretrained weights not implemented")
    
    return model
