"""
Edge Inference Task Model for DNN Inference Optimization
Implements ResNet-50 with early exits and partition point management
"""

import numpy as np
from typing import Dict, Any, <PERSON>, Tuple, Optional
from config import config
from resnet_layer_info import RESNET50_LAYERS, get_layer_info, get_conv_layers, calculate_flops_up_to_layer, get_output_size_at_layer

class EdgeInferenceTask:
    """
    Edge inference task model with ResNet-50 and early exit support
    Handles partition point selection and accuracy requirements
    """
    
    def __init__(self, task_id: int, accuracy_requirement: float = None):
        """
        Initialize inference task
        
        Args:
            task_id: Unique task identifier
            accuracy_requirement: Minimum required accuracy (if None, uses default)
        """
        self.task_id = task_id
        self.config = config.resnet
        
        # Accuracy requirement
        if accuracy_requirement is None:
            self.accuracy_requirement = config.optimization.default_acc_min
        else:
            self.accuracy_requirement = accuracy_requirement
        
        # Early exit selection based on accuracy requirement
        self.early_exit_name = config.get_early_exit_for_accuracy(self.accuracy_requirement)
        self.early_exit_point = self.config.early_exit_points[self.early_exit_name]
        self.achieved_accuracy = self.config.early_exit_accuracies[self.early_exit_name]
        
        # Valid partition points for this early exit
        self.valid_partition_points = config.get_valid_partitions_for_exit(self.early_exit_point)
        
        # Current decision variables
        self.partition_point = 0  # 0 means full local execution
        self.target_server = None  # None means local execution
        self.device_frequency = config.device.f_min
        
        # Task state
        self.is_completed = False
        self.execution_time = 0.0
        self.energy_consumed = 0.0
        
    def set_partition_point(self, partition_point: int) -> bool:
        """
        Set partition point for the task
        
        Args:
            partition_point: Layer index for partitioning (0 for full local, early_exit_point+1 for full offload)
            
        Returns:
            True if partition point is valid, False otherwise
        """
        # Special cases
        if partition_point == 0:  # Full local execution
            self.partition_point = 0
            self.target_server = None
            return True
        elif partition_point == self.early_exit_point + 1:  # Full offload
            self.partition_point = self.early_exit_point + 1
            return True
        
        # Check if partition point is valid (must be Conv2d layer and <= early exit point)
        if (partition_point in self.valid_partition_points and 
            partition_point <= self.early_exit_point):
            self.partition_point = partition_point
            return True
        
        return False
    
    def set_target_server(self, server_id: Optional[int]) -> bool:
        """
        Set target server for offloading
        
        Args:
            server_id: Server ID (0-3) or None for local execution
            
        Returns:
            True if server ID is valid, False otherwise
        """
        if server_id is None or (0 <= server_id < config.server.num_servers):
            self.target_server = server_id
            return True
        return False
    
    def set_device_frequency(self, frequency: float) -> bool:
        """
        Set device frequency for local computation
        
        Args:
            frequency: Device frequency in Hz
            
        Returns:
            True if frequency is valid, False otherwise
        """
        if config.device.f_min <= frequency <= config.device.f_max:
            self.device_frequency = frequency
            return True
        return False
    
    def get_local_flops(self) -> float:
        """
        Get FLOPs for local computation based on partition point
        
        Returns:
            FLOPs for local computation
        """
        if self.partition_point == 0:
            # Full local execution up to early exit point
            return calculate_flops_up_to_layer(self.early_exit_point)
        elif self.partition_point == self.early_exit_point + 1:
            # Full offload - no local computation
            return 0.0
        else:
            # Partial local execution up to partition point
            return calculate_flops_up_to_layer(self.partition_point)
    
    def get_edge_flops(self) -> float:
        """
        Get FLOPs for edge computation based on partition point
        
        Returns:
            FLOPs for edge computation
        """
        if self.partition_point == 0:
            # Full local execution - no edge computation
            return 0.0
        elif self.partition_point == self.early_exit_point + 1:
            # Full offload - all computation on edge
            return calculate_flops_up_to_layer(self.early_exit_point)
        else:
            # Partial offload - computation from partition point to early exit
            local_flops = calculate_flops_up_to_layer(self.partition_point)
            total_flops = calculate_flops_up_to_layer(self.early_exit_point)
            return total_flops - local_flops
    
    def get_transmission_data_size(self) -> int:
        """
        Get data size for transmission based on partition point
        
        Returns:
            Data size in bits for transmission (0 if no transmission needed)
        """
        if self.partition_point == 0 or self.partition_point == self.early_exit_point + 1:
            # No transmission needed for full local or full offload cases
            if self.partition_point == self.early_exit_point + 1:
                # Full offload: transmit input data
                return get_output_size_at_layer(0)  # Input size
            return 0
        else:
            # Transmit intermediate data at partition point
            return get_output_size_at_layer(self.partition_point)
    
    def calculate_local_delay(self, device_frequency: float, g_device: float) -> float:
        """
        Calculate local processing delay
        
        Args:
            device_frequency: Device frequency in Hz
            g_device: Device computing capability (FLOPS per cycle)
            
        Returns:
            Local processing delay in seconds
        """
        local_flops = self.get_local_flops()
        if local_flops > 0:
            return local_flops / (device_frequency * g_device)
        return 0.0
    
    def calculate_communication_delay(self, transmission_rate: float) -> float:
        """
        Calculate communication delay
        
        Args:
            transmission_rate: Transmission rate in bps
            
        Returns:
            Communication delay in seconds
        """
        data_size = self.get_transmission_data_size()
        if data_size > 0:
            return data_size / transmission_rate
        return 0.0
    
    def calculate_edge_delay(self, server_frequency: float, g_server: float) -> float:
        """
        Calculate edge processing delay
        
        Args:
            server_frequency: Server frequency in Hz
            g_server: Server computing capability (FLOPS per cycle)
            
        Returns:
            Edge processing delay in seconds
        """
        edge_flops = self.get_edge_flops()
        if edge_flops > 0:
            return edge_flops / (server_frequency * g_server)
        return 0.0
    
    def calculate_total_delay(self, device_frequency: float, g_device: float,
                            transmission_rate: float, server_frequency: float, 
                            g_server: float) -> float:
        """
        Calculate total end-to-end delay
        
        Args:
            device_frequency: Device frequency in Hz
            g_device: Device computing capability
            transmission_rate: Transmission rate in bps
            server_frequency: Server frequency in Hz
            g_server: Server computing capability
            
        Returns:
            Total delay in seconds
        """
        local_delay = self.calculate_local_delay(device_frequency, g_device)
        comm_delay = self.calculate_communication_delay(transmission_rate)
        edge_delay = self.calculate_edge_delay(server_frequency, g_server)
        
        return local_delay + comm_delay + edge_delay
    
    def is_accuracy_satisfied(self) -> bool:
        """Check if current configuration satisfies accuracy requirement"""
        return self.achieved_accuracy >= self.accuracy_requirement
    
    def get_accuracy_violation_penalty(self) -> float:
        """
        Calculate penalty for accuracy violation
        
        Returns:
            Penalty value (0 if accuracy satisfied, positive if violated)
        """
        if not self.is_accuracy_satisfied():
            accuracy_gap = self.accuracy_requirement - self.achieved_accuracy
            return config.environment.accuracy_violation_penalty * accuracy_gap
        return 0.0
    
    def get_info(self) -> Dict[str, Any]:
        """Get comprehensive task information"""
        return {
            'task_id': self.task_id,
            'accuracy_requirement': self.accuracy_requirement,
            'achieved_accuracy': self.achieved_accuracy,
            'early_exit_name': self.early_exit_name,
            'early_exit_point': self.early_exit_point,
            'partition_point': self.partition_point,
            'target_server': self.target_server,
            'device_frequency': self.device_frequency,
            'local_flops': self.get_local_flops(),
            'edge_flops': self.get_edge_flops(),
            'transmission_data_size': self.get_transmission_data_size(),
            'is_accuracy_satisfied': self.is_accuracy_satisfied(),
            'accuracy_penalty': self.get_accuracy_violation_penalty(),
            'is_completed': self.is_completed,
            'execution_time': self.execution_time,
            'energy_consumed': self.energy_consumed
        }
    
    def reset(self):
        """Reset task to initial state"""
        self.partition_point = 0
        self.target_server = None
        self.device_frequency = config.device.f_min
        self.is_completed = False
        self.execution_time = 0.0
        self.energy_consumed = 0.0
    
    def __str__(self) -> str:
        """String representation of task"""
        info = self.get_info()
        return (f"Task {info['task_id']}: "
                f"Exit={info['early_exit_name']}, "
                f"Partition={info['partition_point']}, "
                f"Server={info['target_server']}, "
                f"Acc={info['achieved_accuracy']:.3f}")
