from collections import deque
import random
from typing import List, Tuple

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.distributions import Normal

from config import config

LOG_SIG_MAX = 2
LOG_SIG_MIN = -20
epsilon = 1e-6

# --- SAC Network Architectures ---

class Actor(nn.Module):
    """Gaussian Policy Actor for SAC."""
    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int]):
        super(Actor, self).__init__()
        layers = []
        prev_dim = state_dim
        for hidden_dim in hidden_dims:
            layers.extend([nn.Linear(prev_dim, hidden_dim), nn.ReLU()])
            prev_dim = hidden_dim
        self.network = nn.Sequential(*layers)
        self.mean_linear = nn.Linear(prev_dim, action_dim)
        self.log_std_linear = nn.Linear(prev_dim, action_dim)

    def forward(self, state):
        x = self.network(state)
        mean = self.mean_linear(x)
        log_std = self.log_std_linear(x)
        log_std = torch.clamp(log_std, min=LOG_SIG_MIN, max=LOG_SIG_MAX)
        return mean, log_std

    def sample(self, state):
        mean, log_std = self.forward(state)
        std = log_std.exp()
        normal = Normal(mean, std)
        x_t = normal.rsample()
        y_t = torch.tanh(x_t)
        action = y_t
        log_prob = normal.log_prob(x_t)
        log_prob -= torch.log(1 - y_t.pow(2) + epsilon)
        log_prob = log_prob.sum(1, keepdim=True)
        return action, log_prob

class Critic(nn.Module):
    """Double Q-Network Critic for SAC."""
    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int]):
        super(Critic, self).__init__()
        # Q1 architecture
        layers1 = []
        prev_dim = state_dim + action_dim
        for hidden_dim in hidden_dims:
            layers1.extend([nn.Linear(prev_dim, hidden_dim), nn.ReLU()])
            prev_dim = hidden_dim
        layers1.append(nn.Linear(prev_dim, 1))
        self.q1 = nn.Sequential(*layers1)

        # Q2 architecture
        layers2 = []
        prev_dim = state_dim + action_dim
        for hidden_dim in hidden_dims:
            layers2.extend([nn.Linear(prev_dim, hidden_dim), nn.ReLU()])
            prev_dim = hidden_dim
        layers2.append(nn.Linear(prev_dim, 1))
        self.q2 = nn.Sequential(*layers2)

    def forward(self, state, action):
        sa = torch.cat([state, action], 1)
        q1 = self.q1(sa)
        q2 = self.q2(sa)
        return q1, q2

# --- Replay Buffer ---

class ReplayBuffer:
    def __init__(self, capacity: int):
        self.buffer = deque(maxlen=capacity)

    def push(self, experience: Tuple):
        self.buffer.append(experience)

    def sample(self, batch_size: int) -> List[Tuple]:
        return random.sample(self.buffer, batch_size)

    def __len__(self):
        return len(self.buffer)

# --- SAC Agent and Controller ---

class SACAgent:
    """A single SAC agent."""
    def __init__(self, agent_id: int, state_dim: int, action_dim: int):
        self.agent_id = agent_id
        self.gamma = config.sac.gamma
        self.tau = config.sac.tau
        self.alpha = config.sac.alpha
        
        # Set target entropy
        if config.sac.automatic_entropy_tuning:
            self.target_entropy = -torch.prod(torch.Tensor((action_dim,))).item()
            self.log_alpha = torch.zeros(1, requires_grad=True)
            self.alpha_optimizer = optim.Adam([self.log_alpha], lr=config.sac.lr_alpha)
        else:
            self.target_entropy = config.sac.target_entropy


        # Actor Network
        self.actor = Actor(state_dim, action_dim, config.sac.actor_hidden_dims)
        self.critic = Critic(state_dim, action_dim, config.sac.critic_hidden_dims)
        self.critic_target = Critic(state_dim, action_dim, config.sac.critic_hidden_dims)
        self.critic_target.load_state_dict(self.critic.state_dict())

        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=config.sac.lr_actor)
        self.critic_optimizer = optim.Adam(self.critic.parameters(), lr=config.sac.lr_critic)

        self.automatic_entropy_tuning = config.sac.automatic_entropy_tuning
        if self.automatic_entropy_tuning:
            self.target_entropy = -torch.prod(torch.Tensor((action_dim,))).item()
            self.log_alpha = torch.zeros(1, requires_grad=True)
            self.alpha_optimizer = optim.Adam([self.log_alpha], lr=config.sac.lr_alpha)

    def act(self, state: np.ndarray, deterministic: bool = False):
        state = torch.FloatTensor(state).unsqueeze(0)
        if deterministic:
            # Use mean action for evaluation (no noise)
            mean, _ = self.actor.forward(state)
            action = torch.tanh(mean)
        else:
            # Use stochastic action for training (with noise)
            action, _ = self.actor.sample(state)
        return action.detach().cpu().numpy()[0]

    def update(self, experiences):
        states, actions, rewards, next_states, dones = experiences

        with torch.no_grad():
            next_actions, next_log_prob = self.actor.sample(next_states)
            q1_next_target, q2_next_target = self.critic_target(next_states, next_actions)
            min_q_next_target = torch.min(q1_next_target, q2_next_target) - self.alpha * next_log_prob
            next_q_value = rewards + (1 - dones) * self.gamma * min_q_next_target

        q1, q2 = self.critic(states, actions)
        q1_loss = F.mse_loss(q1, next_q_value)
        q2_loss = F.mse_loss(q2, next_q_value)
        critic_loss = q1_loss + q2_loss

        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        self.critic_optimizer.step()

        pi, log_pi = self.actor.sample(states)
        q1_pi, q2_pi = self.critic(states, pi)
        min_q_pi = torch.min(q1_pi, q2_pi)
        actor_loss = ((self.alpha * log_pi) - min_q_pi).mean()

        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        self.actor_optimizer.step()

        if self.automatic_entropy_tuning:
            alpha_loss = -(self.log_alpha * (log_pi + self.target_entropy).detach()).mean()
            self.alpha_optimizer.zero_grad()
            alpha_loss.backward()
            self.alpha_optimizer.step()
            self.alpha = self.log_alpha.exp()

        with torch.no_grad():
            for target_param, param in zip(self.critic_target.parameters(), self.critic.parameters()):
                target_param.data.copy_(self.tau * param.data + (1.0 - self.tau) * target_param.data)

class SAC:
    """Controller for Independent SAC agents."""
    def __init__(self, num_agents: int, state_dim: int, action_dim: int):
        self.agents = [SACAgent(i, state_dim, action_dim) for i in range(num_agents)]
        self.replay_buffer = ReplayBuffer(config.sac.buffer_size)
        self.batch_size = config.sac.batch_size
        self.step_count = 0

    def act(self, states: List[np.ndarray], add_noise: bool = True) -> List[np.ndarray]:
        # Use deterministic policy when add_noise=False (for evaluation)
        deterministic = not add_noise
        return [agent.act(state, deterministic=deterministic) for agent, state in zip(self.agents, states)]

    def step(self, states: List[np.ndarray], actions: List[np.ndarray], 
             rewards: List[float], next_states: List[np.ndarray], 
             dones: List[bool]):
        self.replay_buffer.push((states, actions, rewards, next_states, dones))
        self.step_count += 1
        if len(self.replay_buffer) > self.batch_size:
            self.update()

    def update(self):
        experiences = self.replay_buffer.sample(self.batch_size)
        
        # Unzip and reformat the batch for independent learning
        states, actions, rewards, next_states, dones = zip(*experiences)
        
        # Convert to arrays and then to tensors
        states_arr = np.array(states)
        actions_arr = np.array(actions)
        rewards_arr = np.array(rewards)
        next_states_arr = np.array(next_states)
        dones_arr = np.array(dones)

        for i, agent in enumerate(self.agents):
            agent_states = torch.FloatTensor(states_arr[:, i, :])
            agent_actions = torch.FloatTensor(actions_arr[:, i, :])
            agent_rewards = torch.FloatTensor(rewards_arr[:, i]).unsqueeze(1)
            agent_next_states = torch.FloatTensor(next_states_arr[:, i, :])
            agent_dones = torch.FloatTensor(dones_arr[:, i]).unsqueeze(1)
            
            agent_experiences = (agent_states, agent_actions, agent_rewards, agent_next_states, agent_dones)
            agent.update(agent_experiences)

    def save_models(self, filepath: str):
        checkpoint = {'agents': []}
        for i, agent in enumerate(self.agents):
            agent_data = {
                'actor_state_dict': agent.actor.state_dict(),
                'critic_state_dict': agent.critic.state_dict(),
            }
            checkpoint['agents'].append(agent_data)
        torch.save(checkpoint, filepath)

    def load_models(self, filepath: str):
        checkpoint = torch.load(filepath)
        for i, agent in enumerate(self.agents):
            agent_data = checkpoint['agents'][i]
            agent.actor.load_state_dict(agent_data['actor_state_dict'])
            agent.critic.load_state_dict(agent_data['critic_state_dict'])
            agent.critic_target.load_state_dict(agent.critic.state_dict())
